import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';

class CustomButton extends ConsumerStatefulWidget {
  const CustomButton({
    super.key,
    required this.title,
    required this.onTap,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.isOutlined = false,
    this.isLoading = false,
    this.enabled = true,
    this.width,
    this.height,
    this.fontSize,
    this.borderRadius,
    this.icon,
  });

  final String title;
  final Future<void> Function() onTap;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final bool isOutlined;
  final bool isLoading;
  final bool enabled;
  final double? width;
  final double? height;
  final double? fontSize;
  final double? borderRadius;
  final IconData? icon;

  @override
  ConsumerState<CustomButton> createState() => _CustomButtonState();
}

class _CustomButtonState extends ConsumerState<CustomButton> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final isLoading = widget.isLoading || _isLoading;
    final isEnabled = widget.enabled && !isLoading;

    return SizedBox(
      width: widget.width ?? double.infinity,
      height: widget.height ?? 48.h,
      child: ElevatedButton(
        onPressed: isEnabled ? _handleTap : null,
        style: _buildButtonStyle(isEnabled),
        child: _buildButtonContent(isLoading),
      ),
    );
  }

  ButtonStyle _buildButtonStyle(bool isEnabled) {
    final backgroundColor = widget.isOutlined
        ? Colors.transparent
        : (widget.backgroundColor ?? AppColors.primary);
    
    final foregroundColor = widget.isOutlined
        ? (widget.textColor ?? AppColors.primary)
        : (widget.textColor ?? Colors.white);

    return ElevatedButton.styleFrom(
      backgroundColor: isEnabled ? backgroundColor : AppColors.textHint,
      foregroundColor: isEnabled ? foregroundColor : Colors.white,
      elevation: widget.isOutlined ? 0 : 2,
      shadowColor: AppColors.primary.withOpacity(0.3),
      side: widget.isOutlined
          ? BorderSide(
              color: isEnabled
                  ? (widget.borderColor ?? AppColors.primary)
                  : AppColors.textHint,
              width: 1.5,
            )
          : null,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius ?? 12.r),
      ),
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
    );
  }

  Widget _buildButtonContent(bool isLoading) {
    if (isLoading) {
      return SizedBox(
        height: 20.h,
        width: 20.h,
        child: const CircularProgressIndicator(
          color: Colors.white,
          strokeWidth: 2,
        ),
      );
    }

    if (widget.icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.icon,
            size: 20.w,
          ),
          SizedBox(width: 8.w),
          Text(
            widget.title,
            style: TextStyle(
              fontSize: widget.fontSize ?? 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }

    return Text(
      widget.title,
      style: TextStyle(
        fontSize: widget.fontSize ?? 16.sp,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Future<void> _handleTap() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await widget.onTap();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
