import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/constants/app_colors.dart';

class CustomAvatar extends StatelessWidget {
  const CustomAvatar({
    super.key,
    this.imageUrl,
    this.initials,
    this.height,
    this.width,
    this.backgroundColor,
    this.textColor,
    this.onTap,
  });

  final String? imageUrl;
  final String? initials;
  final double? height;
  final double? width;
  final Color? backgroundColor;
  final Color? textColor;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final size = height ?? width ?? 46.h;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: size,
        width: size,
        decoration: BoxDecoration(
          color: backgroundColor ?? AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(size / 2),
          border: Border.all(
            color: AppColors.primary.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(size / 2),
          child: _buildAvatarContent(size),
        ),
      ),
    );
  }

  Widget _buildAvatarContent(double size) {
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: imageUrl!,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildPlaceholder(size),
        errorWidget: (context, url, error) => _buildInitialsAvatar(size),
      );
    }
    
    return _buildInitialsAvatar(size);
  }

  Widget _buildPlaceholder(double size) {
    return Container(
      color: AppColors.primary.withOpacity(0.1),
      child: Center(
        child: SizedBox(
          width: size * 0.4,
          height: size * 0.4,
          child: const CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ),
      ),
    );
  }

  Widget _buildInitialsAvatar(double size) {
    return Container(
      color: backgroundColor ?? AppColors.primary.withOpacity(0.1),
      child: Center(
        child: Text(
          initials ?? '?',
          style: TextStyle(
            color: textColor ?? AppColors.primary,
            fontSize: size * 0.4,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
