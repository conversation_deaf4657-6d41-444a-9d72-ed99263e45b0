import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/models/user.dart';
import '../../../core/models/user_role.dart';

part 'auth_provider.g.dart';

@riverpod
class AuthNotifier extends _$AuthNotifier {
  @override
  AsyncValue<User?> build() {
    return const AsyncValue.data(null);
  }

  Future<void> signIn(String email, String password) async {
    state = const AsyncValue.loading();
    
    try {
      // Simulation d'une connexion
      await Future.delayed(const Duration(seconds: 2));
      
      // Utilisateur de test
      final user = User(
        id: '1',
        email: email,
        firstName: 'Dr. <PERSON>',
        lastName: 'Dupont',
        role: email.contains('cabinet') ? UserRole.cabinet : UserRole.manipulateur,
        phone: '+33 6 12 34 56 78',
        address: '123 Rue de la Santé',
        city: 'Paris',
        postalCode: '75001',
        isVerified: true,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      state = AsyncValue.data(user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> signUp({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required UserRole role,
    String? phone,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      // Simulation d'une inscription
      await Future.delayed(const Duration(seconds: 2));
      
      final user = User(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        email: email,
        firstName: firstName,
        lastName: lastName,
        role: role,
        phone: phone,
        isVerified: false,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      state = AsyncValue.data(user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> signOut() async {
    state = const AsyncValue.data(null);
  }

  Future<void> updateProfile(User updatedUser) async {
    if (state.value != null) {
      state = AsyncValue.data(updatedUser);
    }
  }
}

// Provider pour vérifier si l'utilisateur est connecté
@riverpod
bool isAuthenticated(IsAuthenticatedRef ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.value != null;
}

// Provider pour obtenir l'utilisateur actuel
@riverpod
User? currentUser(CurrentUserRef ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.value;
}

// Provider pour obtenir le rôle de l'utilisateur actuel
@riverpod
UserRole? currentUserRole(CurrentUserRoleRef ref) {
  final user = ref.watch(currentUserProvider);
  return user?.role;
}
