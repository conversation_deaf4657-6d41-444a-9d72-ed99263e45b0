import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/models/mission.dart';

part 'missions_provider.g.dart';

@riverpod
class MissionsNotifier extends _$MissionsNotifier {
  @override
  AsyncValue<List<Mission>> build() {
    _loadMissions();
    return const AsyncValue.loading();
  }

  Future<void> _loadMissions() async {
    state = const AsyncValue.loading();
    
    try {
      // Simulation du chargement des missions
      await Future.delayed(const Duration(seconds: 1));
      
      final missions = _generateSampleMissions();
      state = AsyncValue.data(missions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> refreshMissions() async {
    await _loadMissions();
  }

  Future<void> addMission(Mission mission) async {
    final currentMissions = state.value ?? [];
    state = AsyncValue.data([mission, ...currentMissions]);
  }

  Future<void> updateMission(Mission updatedMission) async {
    final currentMissions = state.value ?? [];
    final updatedMissions = currentMissions.map((mission) {
      return mission.id == updatedMission.id ? updatedMission : mission;
    }).toList();
    state = AsyncValue.data(updatedMissions);
  }

  Future<void> deleteMission(String missionId) async {
    final currentMissions = state.value ?? [];
    final updatedMissions = currentMissions.where((mission) => mission.id != missionId).toList();
    state = AsyncValue.data(updatedMissions);
  }

  List<Mission> _generateSampleMissions() {
    return [
      Mission(
        id: '1',
        title: 'Manipulateur Scanner - Urgence',
        description: 'Recherche manipulateur expérimenté pour scanner d\'urgence. Équipe dynamique, matériel récent.',
        cabinetId: 'cab1',
        cabinetName: 'Centre Radiologique Paris',
        type: MissionType.scanner,
        status: MissionStatus.published,
        urgency: UrgencyLevel.urgent,
        startDate: DateTime.now().add(const Duration(days: 1)),
        endDate: DateTime.now().add(const Duration(days: 1, hours: 8)),
        hourlyRate: 35.0,
        location: 'Paris 15ème',
        address: '123 Rue de Vaugirard, 75015 Paris',
        requiredSkills: ['Scanner', 'Urgences', 'Injection'],
        equipment: 'Scanner Siemens 128 coupes',
        applicationsCount: 3,
        isFeatured: true,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
      Mission(
        id: '2',
        title: 'Radiographie conventionnelle',
        description: 'Mission de remplacement pour radiographie conventionnelle. Cabinet moderne, ambiance conviviale.',
        cabinetId: 'cab2',
        cabinetName: 'Imagerie Médicale Lyon',
        type: MissionType.radiographie,
        status: MissionStatus.published,
        urgency: UrgencyLevel.medium,
        startDate: DateTime.now().add(const Duration(days: 3)),
        endDate: DateTime.now().add(const Duration(days: 3, hours: 7)),
        hourlyRate: 28.0,
        location: 'Lyon 6ème',
        address: '45 Cours Franklin Roosevelt, 69006 Lyon',
        requiredSkills: ['Radiographie', 'Ostéoarticulaire'],
        equipment: 'Radiographie numérique DR',
        applicationsCount: 1,
        isFeatured: false,
        createdAt: DateTime.now().subtract(const Duration(hours: 5)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 3)),
      ),
      Mission(
        id: '3',
        title: 'IRM - Spécialiste neurologie',
        description: 'Recherche manipulateur spécialisé en IRM neurologique. Examens complexes, formation continue assurée.',
        cabinetId: 'cab3',
        cabinetName: 'IRM Marseille Sud',
        type: MissionType.irm,
        status: MissionStatus.published,
        urgency: UrgencyLevel.high,
        startDate: DateTime.now().add(const Duration(days: 2)),
        endDate: DateTime.now().add(const Duration(days: 2, hours: 6)),
        hourlyRate: 42.0,
        location: 'Marseille 8ème',
        address: '78 Avenue du Prado, 13008 Marseille',
        requiredSkills: ['IRM', 'Neurologie', 'Injection gadolinium'],
        equipment: 'IRM 3T Philips',
        applicationsCount: 5,
        isFeatured: true,
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 30)),
      ),
      Mission(
        id: '4',
        title: 'Échographie abdominale',
        description: 'Mission d\'échographie abdominale et pelvienne. Poste à temps partiel, horaires flexibles.',
        cabinetId: 'cab4',
        cabinetName: 'Cabinet Dr. Martin',
        type: MissionType.echographie,
        status: MissionStatus.published,
        urgency: UrgencyLevel.low,
        startDate: DateTime.now().add(const Duration(days: 7)),
        endDate: DateTime.now().add(const Duration(days: 7, hours: 4)),
        hourlyRate: 32.0,
        location: 'Toulouse Centre',
        address: '12 Place du Capitole, 31000 Toulouse',
        requiredSkills: ['Échographie', 'Abdominale', 'Pelvienne'],
        equipment: 'Échographe Mindray DC-70',
        applicationsCount: 0,
        isFeatured: false,
        createdAt: DateTime.now().subtract(const Duration(hours: 8)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 6)),
      ),
    ];
  }
}

// Provider pour les missions en vedette
@riverpod
List<Mission> featuredMissions(FeaturedMissionsRef ref) {
  final missionsAsync = ref.watch(missionsNotifierProvider);
  return missionsAsync.when(
    data: (missions) => missions.where((mission) => mission.isFeatured).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
}

// Provider pour les missions récentes
@riverpod
List<Mission> recentMissions(RecentMissionsRef ref) {
  final missionsAsync = ref.watch(missionsNotifierProvider);
  return missionsAsync.when(
    data: (missions) => missions.where((mission) => !mission.isFeatured).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
}

// Provider pour filtrer les missions par type
@riverpod
List<Mission> missionsByType(MissionsByTypeRef ref, MissionType type) {
  final missionsAsync = ref.watch(missionsNotifierProvider);
  return missionsAsync.when(
    data: (missions) => missions.where((mission) => mission.type == type).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
}
