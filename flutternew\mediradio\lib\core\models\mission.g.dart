// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mission.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MissionImpl _$$MissionImplFromJson(Map<String, dynamic> json) =>
    _$MissionImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      cabinetId: json['cabinetId'] as String,
      cabinetName: json['cabinetName'] as String,
      type: $enumDecode(_$MissionTypeEnumMap, json['type']),
      status: $enumDecode(_$MissionStatusEnumMap, json['status']),
      urgency: $enumDecode(_$UrgencyLevelEnumMap, json['urgency']),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      hourlyRate: (json['hourlyRate'] as num).toDouble(),
      location: json['location'] as String,
      address: json['address'] as String,
      manipulateurId: json['manipulateurId'] as String?,
      manipulateurName: json['manipulateurName'] as String?,
      requiredSkills: (json['requiredSkills'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      equipment: json['equipment'] as String?,
      notes: json['notes'] as String?,
      applicationsCount: (json['applicationsCount'] as num?)?.toInt() ?? 0,
      isFeatured: json['isFeatured'] as bool? ?? false,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$MissionImplToJson(_$MissionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'cabinetId': instance.cabinetId,
      'cabinetName': instance.cabinetName,
      'type': _$MissionTypeEnumMap[instance.type]!,
      'status': _$MissionStatusEnumMap[instance.status]!,
      'urgency': _$UrgencyLevelEnumMap[instance.urgency]!,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'hourlyRate': instance.hourlyRate,
      'location': instance.location,
      'address': instance.address,
      'manipulateurId': instance.manipulateurId,
      'manipulateurName': instance.manipulateurName,
      'requiredSkills': instance.requiredSkills,
      'equipment': instance.equipment,
      'notes': instance.notes,
      'applicationsCount': instance.applicationsCount,
      'isFeatured': instance.isFeatured,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$MissionTypeEnumMap = {
  MissionType.radiographie: 'radiographie',
  MissionType.scanner: 'scanner',
  MissionType.irm: 'irm',
  MissionType.echographie: 'echographie',
  MissionType.mammographie: 'mammographie',
  MissionType.osteodensite: 'osteodensite',
};

const _$MissionStatusEnumMap = {
  MissionStatus.draft: 'draft',
  MissionStatus.published: 'published',
  MissionStatus.inProgress: 'in_progress',
  MissionStatus.completed: 'completed',
  MissionStatus.cancelled: 'cancelled',
};

const _$UrgencyLevelEnumMap = {
  UrgencyLevel.low: 'low',
  UrgencyLevel.medium: 'medium',
  UrgencyLevel.high: 'high',
  UrgencyLevel.urgent: 'urgent',
};
