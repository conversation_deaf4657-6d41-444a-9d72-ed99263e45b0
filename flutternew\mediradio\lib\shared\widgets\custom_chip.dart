import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/constants/app_colors.dart';

class CustomChip extends StatelessWidget {
  const CustomChip({
    super.key,
    required this.label,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.onTap,
    this.isSelected = false,
    this.size = ChipSize.medium,
  });

  final String label;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final VoidCallback? onTap;
  final bool isSelected;
  final ChipSize size;

  @override
  Widget build(BuildContext context) {
    final chipColors = _getChipColors();
    final chipSizes = _getChipSizes();

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: chipSizes.horizontalPadding,
          vertical: chipSizes.verticalPadding,
        ),
        margin: EdgeInsets.only(right: 8.w, bottom: 8.h),
        decoration: BoxDecoration(
          color: chipColors.background,
          borderRadius: BorderRadius.circular(chipSizes.borderRadius),
          border: Border.all(
            color: chipColors.border,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: chipSizes.iconSize,
                color: chipColors.text,
              ),
              SizedBox(width: 4.w),
            ],
            Text(
              label,
              style: TextStyle(
                fontSize: chipSizes.fontSize,
                fontWeight: FontWeight.w500,
                color: chipColors.text,
              ),
            ),
          ],
        ),
      ),
    );
  }

  _ChipColors _getChipColors() {
    if (isSelected) {
      return _ChipColors(
        background: backgroundColor ?? AppColors.primary,
        text: textColor ?? Colors.white,
        border: borderColor ?? AppColors.primary,
      );
    }

    return _ChipColors(
      background: backgroundColor ?? AppColors.surface,
      text: textColor ?? AppColors.textSecondary,
      border: borderColor ?? AppColors.textHint.withOpacity(0.3),
    );
  }

  _ChipSizes _getChipSizes() {
    switch (size) {
      case ChipSize.small:
        return _ChipSizes(
          horizontalPadding: 8.w,
          verticalPadding: 4.h,
          fontSize: 10.sp,
          iconSize: 12.w,
          borderRadius: 6.r,
        );
      case ChipSize.medium:
        return _ChipSizes(
          horizontalPadding: 12.w,
          verticalPadding: 6.h,
          fontSize: 12.sp,
          iconSize: 14.w,
          borderRadius: 8.r,
        );
      case ChipSize.large:
        return _ChipSizes(
          horizontalPadding: 16.w,
          verticalPadding: 8.h,
          fontSize: 14.sp,
          iconSize: 16.w,
          borderRadius: 10.r,
        );
    }
  }
}

enum ChipSize { small, medium, large }

class _ChipColors {
  final Color background;
  final Color text;
  final Color border;

  _ChipColors({
    required this.background,
    required this.text,
    required this.border,
  });
}

class _ChipSizes {
  final double horizontalPadding;
  final double verticalPadding;
  final double fontSize;
  final double iconSize;
  final double borderRadius;

  _ChipSizes({
    required this.horizontalPadding,
    required this.verticalPadding,
    required this.fontSize,
    required this.iconSize,
    required this.borderRadius,
  });
}
