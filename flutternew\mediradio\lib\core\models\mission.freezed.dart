// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'mission.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Mission _$MissionFromJson(Map<String, dynamic> json) {
  return _Mission.fromJson(json);
}

/// @nodoc
mixin _$Mission {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get cabinetId => throw _privateConstructorUsedError;
  String get cabinetName => throw _privateConstructorUsedError;
  MissionType get type => throw _privateConstructorUsedError;
  MissionStatus get status => throw _privateConstructorUsedError;
  UrgencyLevel get urgency => throw _privateConstructorUsedError;
  DateTime get startDate => throw _privateConstructorUsedError;
  DateTime get endDate => throw _privateConstructorUsedError;
  double get hourlyRate => throw _privateConstructorUsedError;
  String get location => throw _privateConstructorUsedError;
  String get address => throw _privateConstructorUsedError;
  String? get manipulateurId => throw _privateConstructorUsedError;
  String? get manipulateurName => throw _privateConstructorUsedError;
  List<String>? get requiredSkills => throw _privateConstructorUsedError;
  String? get equipment => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  int get applicationsCount => throw _privateConstructorUsedError;
  bool get isFeatured => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Mission to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Mission
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MissionCopyWith<Mission> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MissionCopyWith<$Res> {
  factory $MissionCopyWith(Mission value, $Res Function(Mission) then) =
      _$MissionCopyWithImpl<$Res, Mission>;
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String cabinetId,
    String cabinetName,
    MissionType type,
    MissionStatus status,
    UrgencyLevel urgency,
    DateTime startDate,
    DateTime endDate,
    double hourlyRate,
    String location,
    String address,
    String? manipulateurId,
    String? manipulateurName,
    List<String>? requiredSkills,
    String? equipment,
    String? notes,
    int applicationsCount,
    bool isFeatured,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$MissionCopyWithImpl<$Res, $Val extends Mission>
    implements $MissionCopyWith<$Res> {
  _$MissionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Mission
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? cabinetId = null,
    Object? cabinetName = null,
    Object? type = null,
    Object? status = null,
    Object? urgency = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? hourlyRate = null,
    Object? location = null,
    Object? address = null,
    Object? manipulateurId = freezed,
    Object? manipulateurName = freezed,
    Object? requiredSkills = freezed,
    Object? equipment = freezed,
    Object? notes = freezed,
    Object? applicationsCount = null,
    Object? isFeatured = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            cabinetId: null == cabinetId
                ? _value.cabinetId
                : cabinetId // ignore: cast_nullable_to_non_nullable
                      as String,
            cabinetName: null == cabinetName
                ? _value.cabinetName
                : cabinetName // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as MissionType,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as MissionStatus,
            urgency: null == urgency
                ? _value.urgency
                : urgency // ignore: cast_nullable_to_non_nullable
                      as UrgencyLevel,
            startDate: null == startDate
                ? _value.startDate
                : startDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            endDate: null == endDate
                ? _value.endDate
                : endDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            hourlyRate: null == hourlyRate
                ? _value.hourlyRate
                : hourlyRate // ignore: cast_nullable_to_non_nullable
                      as double,
            location: null == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String,
            address: null == address
                ? _value.address
                : address // ignore: cast_nullable_to_non_nullable
                      as String,
            manipulateurId: freezed == manipulateurId
                ? _value.manipulateurId
                : manipulateurId // ignore: cast_nullable_to_non_nullable
                      as String?,
            manipulateurName: freezed == manipulateurName
                ? _value.manipulateurName
                : manipulateurName // ignore: cast_nullable_to_non_nullable
                      as String?,
            requiredSkills: freezed == requiredSkills
                ? _value.requiredSkills
                : requiredSkills // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            equipment: freezed == equipment
                ? _value.equipment
                : equipment // ignore: cast_nullable_to_non_nullable
                      as String?,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
            applicationsCount: null == applicationsCount
                ? _value.applicationsCount
                : applicationsCount // ignore: cast_nullable_to_non_nullable
                      as int,
            isFeatured: null == isFeatured
                ? _value.isFeatured
                : isFeatured // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MissionImplCopyWith<$Res> implements $MissionCopyWith<$Res> {
  factory _$$MissionImplCopyWith(
    _$MissionImpl value,
    $Res Function(_$MissionImpl) then,
  ) = __$$MissionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String cabinetId,
    String cabinetName,
    MissionType type,
    MissionStatus status,
    UrgencyLevel urgency,
    DateTime startDate,
    DateTime endDate,
    double hourlyRate,
    String location,
    String address,
    String? manipulateurId,
    String? manipulateurName,
    List<String>? requiredSkills,
    String? equipment,
    String? notes,
    int applicationsCount,
    bool isFeatured,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$MissionImplCopyWithImpl<$Res>
    extends _$MissionCopyWithImpl<$Res, _$MissionImpl>
    implements _$$MissionImplCopyWith<$Res> {
  __$$MissionImplCopyWithImpl(
    _$MissionImpl _value,
    $Res Function(_$MissionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Mission
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? cabinetId = null,
    Object? cabinetName = null,
    Object? type = null,
    Object? status = null,
    Object? urgency = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? hourlyRate = null,
    Object? location = null,
    Object? address = null,
    Object? manipulateurId = freezed,
    Object? manipulateurName = freezed,
    Object? requiredSkills = freezed,
    Object? equipment = freezed,
    Object? notes = freezed,
    Object? applicationsCount = null,
    Object? isFeatured = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$MissionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        cabinetId: null == cabinetId
            ? _value.cabinetId
            : cabinetId // ignore: cast_nullable_to_non_nullable
                  as String,
        cabinetName: null == cabinetName
            ? _value.cabinetName
            : cabinetName // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as MissionType,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as MissionStatus,
        urgency: null == urgency
            ? _value.urgency
            : urgency // ignore: cast_nullable_to_non_nullable
                  as UrgencyLevel,
        startDate: null == startDate
            ? _value.startDate
            : startDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        endDate: null == endDate
            ? _value.endDate
            : endDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        hourlyRate: null == hourlyRate
            ? _value.hourlyRate
            : hourlyRate // ignore: cast_nullable_to_non_nullable
                  as double,
        location: null == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String,
        address: null == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        manipulateurId: freezed == manipulateurId
            ? _value.manipulateurId
            : manipulateurId // ignore: cast_nullable_to_non_nullable
                  as String?,
        manipulateurName: freezed == manipulateurName
            ? _value.manipulateurName
            : manipulateurName // ignore: cast_nullable_to_non_nullable
                  as String?,
        requiredSkills: freezed == requiredSkills
            ? _value._requiredSkills
            : requiredSkills // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        equipment: freezed == equipment
            ? _value.equipment
            : equipment // ignore: cast_nullable_to_non_nullable
                  as String?,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
        applicationsCount: null == applicationsCount
            ? _value.applicationsCount
            : applicationsCount // ignore: cast_nullable_to_non_nullable
                  as int,
        isFeatured: null == isFeatured
            ? _value.isFeatured
            : isFeatured // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MissionImpl implements _Mission {
  const _$MissionImpl({
    required this.id,
    required this.title,
    required this.description,
    required this.cabinetId,
    required this.cabinetName,
    required this.type,
    required this.status,
    required this.urgency,
    required this.startDate,
    required this.endDate,
    required this.hourlyRate,
    required this.location,
    required this.address,
    this.manipulateurId,
    this.manipulateurName,
    final List<String>? requiredSkills,
    this.equipment,
    this.notes,
    this.applicationsCount = 0,
    this.isFeatured = false,
    this.createdAt,
    this.updatedAt,
  }) : _requiredSkills = requiredSkills;

  factory _$MissionImpl.fromJson(Map<String, dynamic> json) =>
      _$$MissionImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final String cabinetId;
  @override
  final String cabinetName;
  @override
  final MissionType type;
  @override
  final MissionStatus status;
  @override
  final UrgencyLevel urgency;
  @override
  final DateTime startDate;
  @override
  final DateTime endDate;
  @override
  final double hourlyRate;
  @override
  final String location;
  @override
  final String address;
  @override
  final String? manipulateurId;
  @override
  final String? manipulateurName;
  final List<String>? _requiredSkills;
  @override
  List<String>? get requiredSkills {
    final value = _requiredSkills;
    if (value == null) return null;
    if (_requiredSkills is EqualUnmodifiableListView) return _requiredSkills;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? equipment;
  @override
  final String? notes;
  @override
  @JsonKey()
  final int applicationsCount;
  @override
  @JsonKey()
  final bool isFeatured;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Mission(id: $id, title: $title, description: $description, cabinetId: $cabinetId, cabinetName: $cabinetName, type: $type, status: $status, urgency: $urgency, startDate: $startDate, endDate: $endDate, hourlyRate: $hourlyRate, location: $location, address: $address, manipulateurId: $manipulateurId, manipulateurName: $manipulateurName, requiredSkills: $requiredSkills, equipment: $equipment, notes: $notes, applicationsCount: $applicationsCount, isFeatured: $isFeatured, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MissionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.cabinetId, cabinetId) ||
                other.cabinetId == cabinetId) &&
            (identical(other.cabinetName, cabinetName) ||
                other.cabinetName == cabinetName) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.urgency, urgency) || other.urgency == urgency) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.hourlyRate, hourlyRate) ||
                other.hourlyRate == hourlyRate) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.manipulateurId, manipulateurId) ||
                other.manipulateurId == manipulateurId) &&
            (identical(other.manipulateurName, manipulateurName) ||
                other.manipulateurName == manipulateurName) &&
            const DeepCollectionEquality().equals(
              other._requiredSkills,
              _requiredSkills,
            ) &&
            (identical(other.equipment, equipment) ||
                other.equipment == equipment) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.applicationsCount, applicationsCount) ||
                other.applicationsCount == applicationsCount) &&
            (identical(other.isFeatured, isFeatured) ||
                other.isFeatured == isFeatured) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    title,
    description,
    cabinetId,
    cabinetName,
    type,
    status,
    urgency,
    startDate,
    endDate,
    hourlyRate,
    location,
    address,
    manipulateurId,
    manipulateurName,
    const DeepCollectionEquality().hash(_requiredSkills),
    equipment,
    notes,
    applicationsCount,
    isFeatured,
    createdAt,
    updatedAt,
  ]);

  /// Create a copy of Mission
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MissionImplCopyWith<_$MissionImpl> get copyWith =>
      __$$MissionImplCopyWithImpl<_$MissionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MissionImplToJson(this);
  }
}

abstract class _Mission implements Mission {
  const factory _Mission({
    required final String id,
    required final String title,
    required final String description,
    required final String cabinetId,
    required final String cabinetName,
    required final MissionType type,
    required final MissionStatus status,
    required final UrgencyLevel urgency,
    required final DateTime startDate,
    required final DateTime endDate,
    required final double hourlyRate,
    required final String location,
    required final String address,
    final String? manipulateurId,
    final String? manipulateurName,
    final List<String>? requiredSkills,
    final String? equipment,
    final String? notes,
    final int applicationsCount,
    final bool isFeatured,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$MissionImpl;

  factory _Mission.fromJson(Map<String, dynamic> json) = _$MissionImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  String get cabinetId;
  @override
  String get cabinetName;
  @override
  MissionType get type;
  @override
  MissionStatus get status;
  @override
  UrgencyLevel get urgency;
  @override
  DateTime get startDate;
  @override
  DateTime get endDate;
  @override
  double get hourlyRate;
  @override
  String get location;
  @override
  String get address;
  @override
  String? get manipulateurId;
  @override
  String? get manipulateurName;
  @override
  List<String>? get requiredSkills;
  @override
  String? get equipment;
  @override
  String? get notes;
  @override
  int get applicationsCount;
  @override
  bool get isFeatured;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of Mission
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MissionImplCopyWith<_$MissionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
