import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_colors.dart';
import '../../core/models/mission.dart';
import 'custom_avatar.dart';
import 'custom_chip.dart';

class CustomMissionCard extends StatelessWidget {
  const CustomMissionCard({
    super.key,
    required this.mission,
    this.isFeatured = false,
    this.onTap,
    this.onSaveTap,
    this.onCabinetTap,
    this.isSaved = false,
  });

  final Mission mission;
  final bool isFeatured;
  final VoidCallback? onTap;
  final VoidCallback? onSaveTap;
  final VoidCallback? onCabinetTap;
  final bool isSaved;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(20.w),
        margin: EdgeInsets.only(right: 16.w, left: 16.w, bottom: 16.h),
        decoration: BoxDecoration(
          color: isFeatured ? AppColors.primary : Colors.white,
          borderRadius: BorderRadius.circular(14.r),
          gradient: isFeatured ? AppColors.primaryGradient : null,
          boxShadow: [
            BoxShadow(
              color: isFeatured
                  ? AppColors.primary.withOpacity(0.15)
                  : Colors.grey.withOpacity(0.05),
              blurRadius: isFeatured ? 10 : 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _CardHeader(
              mission: mission,
              isFeatured: isFeatured,
              onCabinetTap: onCabinetTap,
              onSaveTap: onSaveTap,
              isSaved: isSaved,
            ),
            SizedBox(height: 12.h),
            _CardTitle(mission: mission, isFeatured: isFeatured),
            SizedBox(height: 8.h),
            if (!isFeatured) _CardDescription(mission: mission),
            if (!isFeatured) SizedBox(height: 12.h),
            _CardTags(mission: mission, isFeatured: isFeatured),
            SizedBox(height: 12.h),
            _CardFooter(mission: mission, isFeatured: isFeatured),
          ],
        ),
      ),
    );
  }
}

class _CardHeader extends StatelessWidget {
  const _CardHeader({
    required this.mission,
    required this.isFeatured,
    this.onCabinetTap,
    this.onSaveTap,
    required this.isSaved,
  });

  final Mission mission;
  final bool isFeatured;
  final VoidCallback? onCabinetTap;
  final VoidCallback? onSaveTap;
  final bool isSaved;

  @override
  Widget build(BuildContext context) {
    final publishDate = DateFormat.yMMMMd(
      'fr_FR',
    ).format(mission.createdAt ?? DateTime.now());

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: onCabinetTap,
          child: CustomAvatar(
            initials: mission.cabinetName.substring(0, 2).toUpperCase(),
            height: 46.h,
            backgroundColor: isFeatured
                ? Colors.white.withOpacity(0.2)
                : AppColors.cabinetColor.withOpacity(0.1),
            textColor: isFeatured ? Colors.white : AppColors.cabinetColor,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                mission.cabinetName,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: isFeatured ? Colors.white : AppColors.textPrimary,
                ),
              ),
              SizedBox(height: 2.h),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 14.w,
                    color: isFeatured
                        ? Colors.white70
                        : AppColors.textSecondary,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    publishDate,
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: isFeatured
                          ? Colors.white70
                          : AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        GestureDetector(
          onTap: onSaveTap,
          child: Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: isFeatured
                  ? Colors.white.withOpacity(0.2)
                  : AppColors.surface,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              isSaved ? Icons.bookmark : Icons.bookmark_border,
              size: 20.w,
              color: isFeatured
                  ? Colors.white
                  : (isSaved ? AppColors.primary : AppColors.textSecondary),
            ),
          ),
        ),
      ],
    );
  }
}

class _CardTitle extends StatelessWidget {
  const _CardTitle({required this.mission, required this.isFeatured});

  final Mission mission;
  final bool isFeatured;

  @override
  Widget build(BuildContext context) {
    return Text(
      mission.title,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: isFeatured ? Colors.white : AppColors.textPrimary,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }
}

class _CardDescription extends StatelessWidget {
  const _CardDescription({required this.mission});

  final Mission mission;

  @override
  Widget build(BuildContext context) {
    return Text(
      mission.description,
      style: TextStyle(
        fontSize: 13.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.textPrimary.withOpacity(0.75),
      ),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }
}

class _CardTags extends StatelessWidget {
  const _CardTags({required this.mission, required this.isFeatured});

  final Mission mission;
  final bool isFeatured;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      children: [
        CustomChip(
          label: mission.typeDisplayName,
          icon: Icons.medical_services,
          backgroundColor: isFeatured
              ? Colors.white.withOpacity(0.15)
              : AppColors.surface,
          textColor: isFeatured ? Colors.white : AppColors.textSecondary,
        ),
        CustomChip(
          label: mission.urgencyDisplayName,
          icon: Icons.priority_high,
          backgroundColor: isFeatured
              ? Colors.white.withOpacity(0.15)
              : _getUrgencyColor(mission.urgency).withOpacity(0.1),
          textColor: isFeatured
              ? Colors.white
              : _getUrgencyColor(mission.urgency),
        ),
        CustomChip(
          label: mission.location,
          icon: Icons.location_on,
          backgroundColor: isFeatured
              ? Colors.white.withOpacity(0.15)
              : AppColors.surface,
          textColor: isFeatured ? Colors.white : AppColors.textSecondary,
        ),
      ],
    );
  }

  Color _getUrgencyColor(UrgencyLevel urgency) {
    switch (urgency) {
      case UrgencyLevel.low:
        return AppColors.success;
      case UrgencyLevel.medium:
        return AppColors.warning;
      case UrgencyLevel.high:
        return AppColors.error;
      case UrgencyLevel.urgent:
        return AppColors.urgentMission;
    }
  }
}

class _CardFooter extends StatelessWidget {
  const _CardFooter({required this.mission, required this.isFeatured});

  final Mission mission;
  final bool isFeatured;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '${mission.hourlyRate.toStringAsFixed(0)}€/h',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w700,
            color: isFeatured ? Colors.white : AppColors.primary,
          ),
        ),
        if (mission.applicationsCount > 0)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: isFeatured
                  ? Colors.white.withOpacity(0.15)
                  : AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              '${mission.applicationsCount} candidature${mission.applicationsCount > 1 ? 's' : ''}',
              style: TextStyle(
                fontSize: 11.sp,
                fontWeight: FontWeight.w500,
                color: isFeatured ? Colors.white : AppColors.primary,
              ),
            ),
          ),
      ],
    );
  }
}
