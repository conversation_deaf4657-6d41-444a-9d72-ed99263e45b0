import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_sizes.dart';
import '../../../core/models/user.dart';
import '../../../shared/widgets/custom_avatar.dart';

class HomeHeader extends StatelessWidget {
  const HomeHeader({
    super.key,
    this.user,
  });

  final User? user;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSizes.lg),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppSizes.radiusXl),
          bottomRight: Radius.circular(AppSizes.radiusXl),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTopRow(),
          SizedBox(height: AppSizes.lg),
          _buildWelcomeText(),
          SizedBox(height: AppSizes.md),
          _buildStatsRow(),
        ],
      ),
    );
  }

  Widget _buildTopRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            CustomAvatar(
              initials: user?.initials ?? 'U',
              height: 48.h,
              backgroundColor: Colors.white.withOpacity(0.2),
              textColor: Colors.white,
            ),
            SizedBox(width: AppSizes.md),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Bonjour,',
                  style: TextStyle(
                    fontSize: AppSizes.fontMd,
                    color: Colors.white70,
                  ),
                ),
                Text(
                  user?.firstName ?? 'Utilisateur',
                  style: TextStyle(
                    fontSize: AppSizes.fontLg,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
        Row(
          children: [
            _buildIconButton(Icons.search, () {}),
            SizedBox(width: AppSizes.sm),
            _buildIconButton(Icons.notifications_outlined, () {}),
          ],
        ),
      ],
    );
  }

  Widget _buildIconButton(IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(AppSizes.sm),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(AppSizes.radiusSm),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: AppSizes.iconMd,
        ),
      ),
    );
  }

  Widget _buildWelcomeText() {
    final roleText = user?.role.displayName ?? 'Professionnel de santé';
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Trouvez votre prochaine mission',
          style: TextStyle(
            fontSize: AppSizes.fontXl,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        SizedBox(height: AppSizes.xs),
        Text(
          roleText,
          style: TextStyle(
            fontSize: AppSizes.fontMd,
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _buildStatsRow() {
    return Row(
      children: [
        _buildStatItem('12', 'Missions\ndisponibles'),
        SizedBox(width: AppSizes.xl),
        _buildStatItem('3', 'Candidatures\nen cours'),
        SizedBox(width: AppSizes.xl),
        _buildStatItem('8', 'Missions\nterminées'),
      ],
    );
  }

  Widget _buildStatItem(String value, String label) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: AppSizes.fontXxl,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: AppSizes.fontSm,
            color: Colors.white70,
            height: 1.2,
          ),
        ),
      ],
    );
  }
}
