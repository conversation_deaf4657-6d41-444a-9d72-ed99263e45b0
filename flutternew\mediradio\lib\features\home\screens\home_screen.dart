import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_sizes.dart';
import '../../../core/models/mission.dart';
import '../../../shared/widgets/custom_mission_card.dart';
import '../../auth/providers/auth_provider.dart';
import '../../missions/providers/missions_provider.dart';
import '../widgets/home_header.dart';
import '../widgets/mission_type_chips.dart';
import '../widgets/featured_missions_section.dart';
import '../widgets/recent_missions_section.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  MissionType? _selectedType;

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);
    final missionsAsync = ref.watch(missionsNotifierProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            await ref.read(missionsNotifierProvider.notifier).refreshMissions();
          },
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    HomeHeader(user: user),
                    SizedBox(height: AppSizes.lg),
                    MissionTypeChips(
                      selectedType: _selectedType,
                      onTypeSelected: (type) {
                        setState(() {
                          _selectedType = type;
                        });
                      },
                    ),
                    SizedBox(height: AppSizes.lg),
                  ],
                ),
              ),
              missionsAsync.when(
                data: (missions) => _buildMissionsContent(missions),
                loading: () => const SliverToBoxAdapter(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                error: (error, _) => SliverToBoxAdapter(
                  child: _buildErrorWidget(error),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMissionsContent(List<Mission> missions) {
    final filteredMissions = _selectedType != null
        ? missions.where((mission) => mission.type == _selectedType).toList()
        : missions;

    final featuredMissions = filteredMissions.where((m) => m.isFeatured).toList();
    final recentMissions = filteredMissions.where((m) => !m.isFeatured).toList();

    return SliverList(
      delegate: SliverChildListDelegate([
        if (_selectedType == null) ...[
          if (featuredMissions.isNotEmpty) ...[
            FeaturedMissionsSection(missions: featuredMissions),
            SizedBox(height: AppSizes.xl),
          ],
          if (recentMissions.isNotEmpty) ...[
            RecentMissionsSection(missions: recentMissions),
          ],
        ] else ...[
          _buildFilteredMissionsSection(filteredMissions),
        ],
        SizedBox(height: AppSizes.xxl),
      ]),
    );
  }

  Widget _buildFilteredMissionsSection(List<Mission> missions) {
    if (missions.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSizes.md),
          child: Text(
            'Missions ${_selectedType?.name}',
            style: TextStyle(
              fontSize: AppSizes.fontXl,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        SizedBox(height: AppSizes.md),
        ...missions.map((mission) => CustomMissionCard(
              mission: mission,
              isFeatured: mission.isFeatured,
              onTap: () => _onMissionTap(mission),
              onSaveTap: () => _onSaveMission(mission),
              onCabinetTap: () => _onCabinetTap(mission.cabinetId),
            )),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: EdgeInsets.all(AppSizes.xl),
      margin: EdgeInsets.symmetric(horizontal: AppSizes.md),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppSizes.radiusLg),
        border: Border.all(color: AppColors.textHint.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.search_off,
            size: 48.w,
            color: AppColors.textHint,
          ),
          SizedBox(height: AppSizes.md),
          Text(
            'Aucune mission trouvée',
            style: TextStyle(
              fontSize: AppSizes.fontLg,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppSizes.sm),
          Text(
            'Essayez de modifier vos filtres ou revenez plus tard',
            style: TextStyle(
              fontSize: AppSizes.fontMd,
              color: AppColors.textHint,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(Object error) {
    return Container(
      padding: EdgeInsets.all(AppSizes.xl),
      margin: EdgeInsets.symmetric(horizontal: AppSizes.md),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusLg),
        border: Border.all(color: AppColors.error.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48.w,
            color: AppColors.error,
          ),
          SizedBox(height: AppSizes.md),
          Text(
            'Erreur de chargement',
            style: TextStyle(
              fontSize: AppSizes.fontLg,
              fontWeight: FontWeight.w600,
              color: AppColors.error,
            ),
          ),
          SizedBox(height: AppSizes.sm),
          Text(
            'Impossible de charger les missions. Vérifiez votre connexion.',
            style: TextStyle(
              fontSize: AppSizes.fontMd,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppSizes.lg),
          ElevatedButton(
            onPressed: () {
              ref.read(missionsNotifierProvider.notifier).refreshMissions();
            },
            child: const Text('Réessayer'),
          ),
        ],
      ),
    );
  }

  void _onMissionTap(Mission mission) {
    // TODO: Naviguer vers les détails de la mission
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Mission: ${mission.title}'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _onSaveMission(Mission mission) {
    // TODO: Implémenter la sauvegarde de mission
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Mission sauvegardée: ${mission.title}'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _onCabinetTap(String cabinetId) {
    // TODO: Naviguer vers le profil du cabinet
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Profil cabinet: $cabinetId'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
