import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_role.freezed.dart';
part 'user_role.g.dart';

/// Énumération des rôles utilisateur
enum UserRole {
  @JsonValue('cabinet')
  cabinet,
  @JsonValue('manipulateur')
  manipulateur,
}

extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.cabinet:
        return 'Cabinet de radiologie';
      case UserRole.manipulateur:
        return 'Manipulateur en radiologie';
    }
  }

  String get shortName {
    switch (this) {
      case UserRole.cabinet:
        return 'Cabinet';
      case UserRole.manipulateur:
        return 'Manipulateur';
    }
  }
}
