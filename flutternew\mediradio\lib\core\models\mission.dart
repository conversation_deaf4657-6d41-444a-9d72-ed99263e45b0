import 'package:freezed_annotation/freezed_annotation.dart';

part 'mission.freezed.dart';
part 'mission.g.dart';

enum MissionStatus {
  @JsonValue('draft')
  draft,
  @JsonValue('published')
  published,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
}

enum MissionType {
  @JsonValue('radiographie')
  radiographie,
  @JsonValue('scanner')
  scanner,
  @JsonValue('irm')
  irm,
  @JsonValue('echographie')
  echographie,
  @JsonValue('mammographie')
  mammographie,
  @JsonValue('osteodensite')
  osteodensite,
}

enum UrgencyLevel {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}

@freezed
class Mission with _$Mission {
  const factory Mission({
    required String id,
    required String title,
    required String description,
    required String cabinetId,
    required String cabinetName,
    required MissionType type,
    required MissionStatus status,
    required UrgencyLevel urgency,
    required DateTime startDate,
    required DateTime endDate,
    required double hourlyRate,
    required String location,
    required String address,
    String? manipulateurId,
    String? manipulateurName,
    List<String>? requiredSkills,
    String? equipment,
    String? notes,
    @Default(0) int applicationsCount,
    @Default(false) bool isFeatured,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Mission;

  factory Mission.fromJson(Map<String, dynamic> json) => _$MissionFromJson(json);
}

extension MissionExtension on Mission {
  String get statusDisplayName {
    switch (status) {
      case MissionStatus.draft:
        return 'Brouillon';
      case MissionStatus.published:
        return 'Publiée';
      case MissionStatus.inProgress:
        return 'En cours';
      case MissionStatus.completed:
        return 'Terminée';
      case MissionStatus.cancelled:
        return 'Annulée';
    }
  }

  String get typeDisplayName {
    switch (type) {
      case MissionType.radiographie:
        return 'Radiographie';
      case MissionType.scanner:
        return 'Scanner';
      case MissionType.irm:
        return 'IRM';
      case MissionType.echographie:
        return 'Échographie';
      case MissionType.mammographie:
        return 'Mammographie';
      case MissionType.osteodensite:
        return 'Ostéodensité';
    }
  }

  String get urgencyDisplayName {
    switch (urgency) {
      case UrgencyLevel.low:
        return 'Faible';
      case UrgencyLevel.medium:
        return 'Moyenne';
      case UrgencyLevel.high:
        return 'Élevée';
      case UrgencyLevel.urgent:
        return 'Urgent';
    }
  }

  Duration get duration => endDate.difference(startDate);
  
  bool get isActive => status == MissionStatus.published || status == MissionStatus.inProgress;
  
  bool get canApply => status == MissionStatus.published && manipulateurId == null;
}
