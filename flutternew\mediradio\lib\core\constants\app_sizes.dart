import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Tailles et espacements de l'application
class AppSizes {
  AppSizes._();

  // Espacements
  static double get xs => 4.w;
  static double get sm => 8.w;
  static double get md => 16.w;
  static double get lg => 24.w;
  static double get xl => 32.w;
  static double get xxl => 48.w;

  // Hauteurs
  static double get buttonHeight => 48.h;
  static double get inputHeight => 56.h;
  static double get appBarHeight => 56.h;
  static double get cardHeight => 120.h;
  static double get avatarSize => 46.h;

  // Rayons de bordure
  static double get radiusXs => 4.r;
  static double get radiusSm => 8.r;
  static double get radiusMd => 12.r;
  static double get radiusLg => 16.r;
  static double get radiusXl => 20.r;

  // Tailles d'icônes
  static double get iconXs => 16.w;
  static double get iconSm => 20.w;
  static double get iconMd => 24.w;
  static double get iconLg => 32.w;
  static double get iconXl => 48.w;

  // Tailles de police
  static double get fontXs => 10.sp;
  static double get fontSm => 12.sp;
  static double get fontMd => 14.sp;
  static double get fontLg => 16.sp;
  static double get fontXl => 18.sp;
  static double get fontXxl => 24.sp;
  static double get fontTitle => 28.sp;

  // Largeurs
  static double get maxContentWidth => 400.w;
  static double get cardWidth => 300.w;
}
